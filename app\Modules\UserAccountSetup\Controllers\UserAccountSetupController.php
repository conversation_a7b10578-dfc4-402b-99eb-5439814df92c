<?php

namespace App\Modules\UserAccountSetup\Controllers;

use App\Events\Payment\GenesisAccountBalanceEvent;
use App\Http\Controllers\Controller;
use App\Modules\AccountCredit\Requests\StoreStripeRequest;
use App\Modules\AccountCredit\Services\AccountCreditFundSelectionService;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppService;
use App\Modules\Contact\Requests\CreateContactRequest;
use App\Modules\Stripe\Providers\StripeKeyProvider;
use App\Modules\UserAccountSetup\Requests\UserAccountSetupStoreWireTransferFormRequest;
use App\Modules\UserAccountSetup\Services\UserAccountSetupService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class UserAccountSetupController extends Controller
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Render Contact Form Page
     */
    public function renderSetupFormContact(): Response|RedirectResponse
    {
        if (is_null(Auth::user()->contact_setup)) {
            return Inertia::render(
                'UserRegistration/UserRegistrationFormContactSetup',
                [
                    'setup' => false,
                ]
            );
        } else {
            return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
        }
    }

    /**
     * Setup Contact
     */
    public function setupContactStore(CreateContactRequest $request): RedirectResponse
    {
        $request->store();

        return Redirect::route('user-account-setup.contact.finish')->withHeaders(['X-Inertia-Replace' => 'true']);
    }

    /**
     * Finish Contact Setup
     */
    public function finishSetupContact(): Response|RedirectResponse
    {
        if (is_null(Auth::user()->contact_setup) || Auth::user()->contact_setup == true) {
            return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
        } else {
            return Inertia::render(
                'Notice/ContactSetupMessage',
                [
                    'setup' => true,
                ]
            );
        }
    }

    /**
     * Render Setup 2FA Form Page
     */
    public function renderSetupFormTwoFactorAuth(): Response|RedirectResponse
    {
        if (Auth::user()->two_factor_setup == true) {
            return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
        }

        $secretKey = (new AuthenticatorAppService)->generateSecretKey();
        $qrCodeImageUrl = (new AuthenticatorAppService)->generateQrCodeImageURL($secretKey, Auth::user()->id);

        return Inertia::render(
            'UserAccountSetup/UserAccountSetupTwoFactorAuth',
            compact('secretKey', 'qrCodeImageUrl')
        );
    }

    /**
     * Finish Two-Factor Setup
     */
    public function finishSetupTwoFactor(): RedirectResponse
    {
        (new UserAccountSetupService)->updateInitialSetupTwoFactor(Auth::user()->id);

        return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
    }

    /**
     * Render Setup Payment Method Form Page
     */
    public function renderSetupFormPaymentMethod(): Response|RedirectResponse
    {
        if (Auth::user()->payment_method_setup == true) {
            return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
        }

        $publicKey = (new StripeKeyProvider)->getPromise();

        return Inertia::render(
            'UserAccountSetup/UserAccountSetupPaymentMethod',
            compact('publicKey')
        );
    }

    /**
     * Render Setup Payment Method Success Page
     */
    public function renderSetupFormPaymentSuccessPage(): Response
    {
        (new UserAccountSetupService)->updateInitialSetupPaymentMethod(Auth::user()->id);

        return Inertia::render(
            'PaymentMethod/PaymentMethodCreateSuccessPage',
            [
                'initialSetup' => true,
                'message' => 'You may create more and manage them in your payment methods page',
            ]
        );
    }

    /**
     * Finish Two-Factor Setup
     */
    public function finishSetupPaymentMethod(): RedirectResponse
    {
        (new UserAccountSetupService)->updateInitialSetupPaymentMethod(Auth::user()->id);

        return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
    }

    /**
     * Render Account Credit Setup Form Page
     *
     * @return Response
     */
    public function renderSetupFormAccountCredit(): Response|RedirectResponse
    {
        if (Auth::user()->account_credit_setup == true) {
            return Redirect::route('domain')->withHeaders(['X-Inertia-Replace' => 'true']);
        }

        return Inertia::render(
            'UserAccountSetup/UserAccountSetupAccountCredit',
            AccountCreditFundSelectionService::instance()->getFundSelectionData(true)
        );
    }

    /**
     * Load Account Credit
     *
     *
     * @return void
     */
    public function storeAccountBalanceStripe(StoreStripeRequest $request)
    {
        event(new GenesisAccountBalanceEvent(Auth::user()->id));

        $request->store();
    }

    /**
     * Load Account Credit
     *
     *
     * @return void
     */
    public function storeAccountBalanceWireTransfer(UserAccountSetupStoreWireTransferFormRequest $request)
    {
        event(new GenesisAccountBalanceEvent(Auth::user()->id));

        $request->store();

        (new UserAccountSetupService)->updateInitialSetupAccountCredit(Auth::user()->id);

        //! SHOULD NOT REDIRECT SINCE USER WILL BE SENT TO A SUCCESS PAGE VIA CLIENT SIDE
        // return Inertia::render(
        //     'AccountBalance/AccountBalanceAddFundsSuccessPage',
        //     [
        //         'initialSetup' => true,
        //         'message' => 'Wire transfer details saved',
        //     ]
        // );
    }

    /**
     * Render Setup Payment Method Success Page
     */
    public function renderSetupFormAccountCreditSuccessPage(): Response
    {
        //(new UserAccountSetupService)->updateInitialSetupAccountCredit(Auth::user()->id);

        return Inertia::render(
            'AccountBalance/AccountBalanceAddFundsSuccessPage',
            [
                'initialSetup' => true,
                'message' => 'You may add more funds in the account balance page',
            ]
        );
    }
}
