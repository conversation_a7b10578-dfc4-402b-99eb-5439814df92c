import React from 'react';
import setDefaultDateFormat from '../../../Util/setDefaultDateFormat';
import _PaymentSummary from '../../../Constant/_PaymentSummary';

export const HistoryItem = ({ item, index }) => {

    // console.log(item);

    const getAmount = (item) => {
        if (item.type === 'CREDIT') {
            return '-' + parseFloat(item.amount).toFixed(2);
        } else {
            return '' + parseFloat(item.amount).toFixed(2);
        }
    }

    const getTotalPaid = (item) => {
        var paidAmount = item.invoice_paid_amount ??
            item.market_place_invoice_paid_amount ??
            item.summary_paid_amount ?? item.amount ?? 0;

        if (item.type === 'CREDIT') {
            return '-' + parseFloat(paidAmount).toFixed(2);
        } else {
            return '' + parseFloat(paidAmount).toFixed(2);
        }
    }

    const getFees = (item) => {
        var paidAmount = item.invoice_paid_amount ??
            item.market_place_invoice_paid_amount ??
            item.summary_paid_amount ?? item.amount ?? 0;

        return parseFloat(paidAmount - item.amount).toFixed(2);
    }

    const getType = (item) => {
        var name = item.payment_invoice_id ? 'Payment Invoice' :
            item.payment_market_place_invoice_id ? 'Market Invoice' :
                item.payment_reimbursement_id ? 'Payment Refund' :
                    item.payment_market_place_reimbursement_id ? 'Market Refund' : null;

        return item.summary_type == 'ACCOUNT_BALANCE' ? null :
            name ?? _PaymentSummary.TEXT[item.summary_type] ?? null;
    }



    const getDescription = (item) => {
        const sourceFunds = item.bank_transfer_id ? 'from Bank Transfer' :
            item.stripe_id ? 'from Card' : item.system_credit_id ? 'from System Credit' : '';

        if (item.type === 'CREDIT') {
            return getType(item) ?? 'Account Refund';
        } else if (item.type === 'DEBIT') {
            return getType(item) ?? 'Added Funds ' + sourceFunds;
        } else {
            return 'Initial Transaction';
        }
    }

    return (
        <>
            <tr key={"hi-" + index} className="bg-white mb-2">
                <td>{parseFloat(item.running_balance).toFixed(2)}</td>
                <td className="rounded-l">{getAmount(item)}</td>
                <td className="rounded-l">{getFees(item)}</td>
                <td className="rounded-l pl-2">{getTotalPaid(item)}</td>
                <td>{getDescription(item)}</td>
                <td>
                    {new Date(item.created_at * 1000).toLocaleString()}
                </td>
            </tr>
        </>

    )
}
