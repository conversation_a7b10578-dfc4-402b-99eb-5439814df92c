<?php

namespace App\Modules\Rdap\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class RdapService
{
    public static function instance(): self
    {
        $rdapService = new self;

        return $rdapService;
    }

    public function callRdapInfo(string $domain): array
    {
        try {
            $url = Config::get('rdap.base').'/'.$domain;
            $request = Http::rdap()->get($url);
            
            $jsonResponse = $request->json();
            
            if (empty($jsonResponse)) {
                throw new Exception('Empty response received from RDAP service');
            }
            
            return $jsonResponse;
        } catch (RequestException $e) {
            app(AuthLogger::class)->error('RdapService: '.$e->getMessage());
            throw new FailedRequestException(500, 'Connection failed: The RDAP service is temporarily unavailable. Please try again later.', 'Internal Server Error');      
        } catch (Exception $e) {
            throw new FailedRequestException(520, 'Error Unknown: ' . $e->getMessage(), 'Unexpected response');
        }
    }
}
