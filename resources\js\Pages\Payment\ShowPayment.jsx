import React from "react";
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";
import EmptyList from "../../Components/Payment/EmptyList";
import PaymentContainer from "../../Components/Payment/PaymentContainer";
import _PaymentSummary from "../../Constant/_PaymentSummary";
import AccountBalanceContainer from "../../Components/Payment/AccountBalanceContainer";
import MultiPaymentContainer from "../../Components/Payment/MultiPaymentContainer";

export default function ShowPayment({ data, icann_total, summaryData }) {

    //console.log(summaryData)
    return (
        <AccountCenterLayout hideNav={true}>

            {data.length == 0 ? (
                <EmptyList />
            ) : (
                <>
                    {(summaryData.type == _PaymentSummary.TYPES.ACCOUNT_BALANCE) ? (
                        <AccountBalanceContainer
                            data={data}
                            icann_total={icann_total}
                            summaryData={summaryData}
                        />
                    ) : (summaryData.type == _PaymentSummary.TYPES.MULTI_CHECKOUT_INVOICE) ? (
                        <MultiPaymentContainer
                            data={data}
                            icann_total={icann_total}
                            summaryData={summaryData}
                        />
                    ) : (<PaymentContainer
                        key={'spi-' + data[0].id}
                        data={data}
                        icann_total={icann_total}
                        summaryData={summaryData}
                    />)}
                </>

            )}
        </AccountCenterLayout>
    );
}
